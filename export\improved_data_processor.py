# -*- coding: utf-8 -*-
"""
改进的数据处理模块 - 生成更高质量的训练数据（已集成铁路场景专用规则）
"""
import os
import re
import random
from typing import List, Tuple
import torch
from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer
from config import PunctuationConfig

class PunctuationDataset(Dataset):
    """标点符号数据集"""
    
    def __init__(self, texts: List[str], labels: List[List[str]], tokenizer, max_length: int):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.config = PunctuationConfig()
    
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        text = self.texts[idx]
        labels = self.labels[idx]
        
        # 分词
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        # 处理标签
        label_ids = self._align_labels_with_tokens(text, labels)
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.tensor(label_ids, dtype=torch.long)
        }
    
    def _align_labels_with_tokens(self, text: str, labels: List[str]) -> List[int]:
        """改进的标签与token对齐方法"""
        tokens = self.tokenizer.tokenize(text)
        token_labels = []

        # 确保标签数量与文本字符数量一致
        if len(labels) != len(text):
            # 调整标签长度以匹配文本
            if len(labels) < len(text):
                labels = labels + ['O'] * (len(text) - len(labels))
            else:
                labels = labels[:len(text)]

        # 添加[CLS]标签
        token_labels.append(self.config.LABEL_TO_ID['O'])

        # 字符级到token级的精确映射
        char_idx = 0
        for token in tokens:
            if char_idx < len(text) and char_idx < len(labels):
                # 使用字符对应的标签
                label = labels[char_idx]
                token_labels.append(self.config.LABEL_TO_ID.get(label, self.config.LABEL_TO_ID['O']))
                char_idx += 1
            else:
                token_labels.append(self.config.LABEL_TO_ID['O'])

        # 添加[SEP]标签
        token_labels.append(self.config.LABEL_TO_ID['O'])

        # 填充或截断到max_length
        while len(token_labels) < self.max_length:
            token_labels.append(-100)  # 忽略的标签

        return token_labels[:self.max_length]

class ImprovedDataProcessor:
    """改进的数据处理器"""
    
    def __init__(self):
        self.config = PunctuationConfig()
        
        # 更丰富的词汇库
        self.subjects = [
            "我", "你", "他", "她", "我们", "大家", "同学们", "老师", "朋友", "家人",
            "学生", "工程师", "医生", "设计师", "程序员", "研究员", "孩子们", "年轻人"
        ]
        
        self.verbs = [
            "学习", "工作", "思考", "讨论", "研究", "开发", "设计", "创造", "分析", "解决",
            "理解", "掌握", "实现", "完成", "参与", "合作", "交流", "分享", "探索", "发现",
            "改进", "优化", "测试", "验证", "评估", "比较", "选择", "决定", "计划", "准备"
        ]
        
        self.objects = [
            "问题", "项目", "方案", "计划", "想法", "目标", "任务", "挑战", "机会", "技术",
            "方法", "工具", "系统", "模型", "算法", "数据", "结果", "效果", "经验", "知识",
            "技能", "能力", "水平", "质量", "标准", "要求", "建议", "意见", "观点", "理论"
        ]
        
        self.adjectives = [
            "好", "棒", "优秀", "出色", "精彩", "有趣", "重要", "关键", "复杂", "简单",
            "困难", "容易", "高效", "实用", "创新", "先进", "可靠", "稳定", "灵活", "强大",
            "完整", "详细", "清晰", "准确", "合理", "有效", "成功", "失败", "满意", "惊喜"
        ]
        
        # 连接词和短语
        self.connectors = [
            "然后", "接着", "另外", "此外", "而且", "不过", "但是", "可是", "因为", "所以",
            "如果", "虽然", "尽管", "无论", "由于", "除了", "除此之外", "总之", "最后", "首先"
        ]
        
        # 时间和地点词汇
        self.time_words = [
            "今天", "明天", "昨天", "现在", "刚才", "马上", "立即", "经常", "总是", "有时",
            "早上", "中午", "下午", "晚上", "最近", "以前", "将来", "目前", "当前", "未来"
        ]
        
        self.place_words = [
            "这里", "那里", "家里", "公司", "学校", "办公室", "会议室", "实验室", "图书馆", "教室"
        ]
        
        self.pause_objects = [
            "苹果", "香蕉", "橙子", "葡萄", "西瓜", "草莓", "蓝莓", "柚子",
            "语文", "数学", "英语", "物理", "化学", "生物", "历史", "地理", "政治",
            "北京", "上海", "广州", "深圳", "杭州", "成都", "重庆", "武汉"
        ]
        
        # 铁路专用身份词
        self.railway_roles = [
            "施工负责人", "驻站联络员", "工地防护员", "负责人", "驻站", "带班人"
        ]
    
    def create_complex_sentence(self) -> Tuple[str, List[str]]:
        """创建复杂句子"""
        sentence_types = [
            self._create_simple_sentence,
            self._create_compound_sentence,
            self._create_question_sentence,
            self._create_exclamation_sentence,
            self._create_conditional_sentence,
            self._create_complex_sentence_with_connectors,
            self._create_pause_sentence,
            self._create_railway_sentence  # 新增铁路场景
        ]
        
        sentence_type = random.choice(sentence_types)
        return sentence_type()
    
    def _create_railway_sentence(self) -> Tuple[str, List[str]]:
        """铁路场景专用句式"""
        templates = [
            lambda: (
                f"{random.choice(self.time_words)}，{random.choice(self.subjects)}，"
                f"{random.choice(self.verbs)}，{random.choice(self.objects)}，"
                f"作业里程，K{random.randint(1,9)}+{random.randint(100,900)}至K{random.randint(1,9)}+{random.randint(100,900)}，"
                f"施工负责人，{random.choice(self.subjects)}，"
                f"驻站联络员，{random.choice(self.subjects)}，"
                f"上道号，{random.randint(10000,99999)}，是否正确？"
            ),
            lambda: (
                f"{random.choice(self.time_words)}，天窗命令号，{random.randint(100000,999999)}，"
                f"作业项目，道岔手工检查、线路手工综合检查，"
                f"作业人数，{random.randint(5,50)}人，"
                f"工地防护员，{random.choice(self.subjects)}、{random.choice(self.subjects)}，"
                f"呃，核对完毕。"
            )
        ]
        text = random.choice(templates)()
        labels = ['O'] * len(text)
        
        # 强制在','后一位打COMMA
        for i, ch in enumerate(text):
            if ch == '，':
                labels[i] = 'COMMA'
        
        # 强制句尾
        if text.endswith('？'):
            labels[-1] = 'QUESTION'
        else:
            labels[-1] = 'PERIOD'
        
        return text, labels
    
    def _create_simple_sentence(self) -> Tuple[str, List[str]]:
        """创建简单句"""
        patterns = [
            # 主谓宾
            lambda: f"{random.choice(self.subjects)}{random.choice(self.verbs)}{random.choice(self.objects)}",
            # 主系表
            lambda: f"{random.choice(self.objects)}很{random.choice(self.adjectives)}",
            # 时间+主谓宾
            lambda: f"{random.choice(self.time_words)}{random.choice(self.subjects)}{random.choice(self.verbs)}{random.choice(self.objects)}",
            # 地点+主谓
            lambda: f"在{random.choice(self.place_words)}{random.choice(self.subjects)}{random.choice(self.verbs)}",
        ]
        
        text = random.choice(patterns)()
        labels = ['O'] * len(text)
        
        # 句末标点
        end_punct = random.choices(
            ['PERIOD', 'QUESTION', 'EXCLAIM'],
            weights=[0.7, 0.15, 0.15]
        )[0]
        labels[-1] = end_punct
        
        return text, labels
    
    def _create_pause_sentence(self) -> Tuple[str, List[str]]:
        """创建含顿号的并列句"""
        items = random.sample(self.pause_objects, 3)
        text = f"{items[0]}{items[1]}{items[2]}都很{random.choice(self.adjectives)}"
        labels = ['O'] * len(text)
        
        # 在第一个和第二个并列项后添加顿号
        pause_pos1 = len(items[0])
        pause_pos2 = len(items[0]) + len(items[1])
        
        if pause_pos1 < len(labels):
            labels[pause_pos1 - 1] = 'PAUSE'
        if pause_pos2 < len(labels):
            labels[pause_pos2 - 1] = 'PAUSE'
        
        # 句末标点
        labels[-1] = 'PERIOD'
    
        return text, labels
    
    def _create_question_sentence(self) -> Tuple[str, List[str]]:
        """创建疑问句"""
        patterns = [
            lambda: f"{random.choice(self.subjects)}觉得{random.choice(self.objects)}怎么样",
            lambda: f"你知道{random.choice(self.objects)}吗",
            lambda: f"这个{random.choice(self.objects)}是不是很{random.choice(self.adjectives)}",
            lambda: f"我们应该如何{random.choice(self.verbs)}这个{random.choice(self.objects)}",
            lambda: f"为什么{random.choice(self.objects)}这么{random.choice(self.adjectives)}",
        ]
        
        text = random.choice(patterns)()
        labels = ['O'] * len(text)
        labels[-1] = 'QUESTION'
        
        return text, labels
    
    def _create_exclamation_sentence(self) -> Tuple[str, List[str]]:
        """创建感叹句"""
        patterns = [
            lambda: f"太{random.choice(self.adjectives)}了",
            lambda: f"真是{random.choice(self.adjectives)}",
            lambda: f"哇{random.choice(self.objects)}好{random.choice(self.adjectives)}",
            lambda: f"这个{random.choice(self.objects)}真{random.choice(self.adjectives)}",
        ]
        
        text = random.choice(patterns)()
        labels = ['O'] * len(text)
        labels[-1] = 'EXCLAIM'
        
        return text, labels
    
    def _create_conditional_sentence(self) -> Tuple[str, List[str]]:
        """创建条件句"""
        condition = f"如果{random.choice(self.objects)}很{random.choice(self.adjectives)}"
        result = f"{random.choice(self.subjects)}就{random.choice(self.verbs)}"
        
        text = condition + result
        labels = ['O'] * len(text)
        
        # 在"如果"从句后加逗号
        comma_pos = len(condition)
        if comma_pos < len(labels):
            labels[comma_pos - 1] = 'COMMA'
        
        labels[-1] = 'PERIOD'
        
        return text, labels
    
    def _create_complex_sentence_with_connectors(self) -> Tuple[str, List[str]]:
        """创建带连接词的复杂句"""
        part1 = f"{random.choice(self.time_words)}{random.choice(self.subjects)}{random.choice(self.verbs)}{random.choice(self.objects)}"
        connector = random.choice(self.connectors)
        part2 = f"{random.choice(self.subjects)}觉得{random.choice(self.objects)}很{random.choice(self.adjectives)}"
        
        text = part1 + connector + part2
        labels = ['O'] * len(text)
        
        # 在连接词前加逗号
        comma_pos = len(part1)
        if comma_pos < len(labels):
            labels[comma_pos - 1] = 'COMMA'
        
        # 句末标点
        end_punct = random.choices(
            ['PERIOD', 'QUESTION', 'EXCLAIM'],
            weights=[0.7, 0.2, 0.1]
        )[0]
        labels[-1] = end_punct
        
        return text, labels
    
    def create_training_data(self, num_samples: int = 1000):
        """创建训练数据"""
        print(f"📝 生成 {num_samples} 条高质量训练数据...")
        
        texts = []
        labels = []
        
        for _ in range(num_samples):
            text, label_list = self.create_complex_sentence()
            texts.append(text)
            labels.append(label_list)
        
        # 追加铁路规则数据
        rail_texts, rail_labels = self.create_railway_samples(800)
        texts.extend(rail_texts)
        labels.extend(rail_labels)
        
        print(f"✅ 生成了 {len(texts)} 条训练数据")
        
        # 统计标签分布
        all_labels = []
        for label_list in labels:
            all_labels.extend(label_list)
        
        from collections import Counter
        label_counter = Counter(all_labels)
        print(f"📊 标签分布:")
        for label in self.config.LABELS:
            count = label_counter.get(label, 0)
            percentage = (count / len(all_labels) * 100) if all_labels else 0
            punct = self.config.LABEL_TO_PUNCT.get(label, '')
            print(f"  {label} ({punct}): {count} ({percentage:.1f}%)")
        
        return texts, labels
    
    def create_railway_samples(self, cnt: int = 800):
        """生成铁路场景规则数据"""
        templates = [
            lambda: (
                f"{random.choice(self.time_words)}，{random.choice(self.subjects)}，"
                f"{random.choice(self.verbs)}，{random.choice(self.objects)}，"
                f"作业里程，K{random.randint(1,9)}+{random.randint(100,900)}至K{random.randint(1,9)}+{random.randint(100,900)}，"
                f"施工负责人，{random.choice(self.subjects)}，"
                f"驻站联络员，{random.choice(self.subjects)}，"
                f"上道号，{random.randint(10000,99999)}，是否正确？"
            ),
            lambda: (
                f"{random.choice(self.time_words)}，天窗命令号，{random.randint(100000,999999)}，"
                f"作业项目，道岔手工检查、线路手工综合检查，"
                f"作业人数，{random.randint(5,50)}人，"
                f"工地防护员，{random.choice(self.subjects)}、{random.choice(self.subjects)}，"
                f"呃，核对完毕。"
            )
        ]
        texts, labels = [], []
        for _ in range(cnt):
            txt = random.choice(templates)()
            labs = ['O'] * len(txt)
            # 强制在','后一位打COMMA
            for i, ch in enumerate(txt):
                if ch == '，':
                    labs[i] = 'COMMA'
            # 强制句尾
            if txt.endswith('？'):
                labs[-1] = 'QUESTION'
            else:
                labs[-1] = 'PERIOD'
            texts.append(txt)
            labels.append(labs)
        return texts, labels
    
    def save_data(self, texts: List[str], labels: List[List[str]], filename: str):
        """保存数据到文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            for text, label_list in zip(texts, labels):
                # 保存格式: 字符\t标签
                for char, label in zip(text, label_list):
                    f.write(f"{char}\t{label}\n")
                f.write("\n")  # 句子分隔符
        print(f"💾 数据已保存到 {filename}")
    
    def load_data(self, filename: str) -> Tuple[List[str], List[List[str]]]:
        """从文件加载数据"""
        texts = []
        labels = []
        current_text = []
        current_labels = []
        
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:  # 空行表示句子结束
                        if current_text:
                            texts.append(''.join(current_text))
                            labels.append(current_labels.copy())
                            current_text = []
                            current_labels = []
                    else:
                        parts = line.split('\t')
                        if len(parts) == 2:
                            char, label = parts
                            current_text.append(char)
                            current_labels.append(label)
                
                # 处理最后一句
                if current_text:
                    texts.append(''.join(current_text))
                    labels.append(current_labels)
            
            print(f"📂 从 {filename} 加载了 {len(texts)} 条数据")
            return texts, labels
            
        except FileNotFoundError:
            print(f"❌ 文件 {filename} 不存在")
            return [], []
    
    def prepare_datasets(self, train_size: int = 1500, valid_size: int = 300) -> Tuple[DataLoader, DataLoader]:
        """准备训练和验证数据集"""
        print("🔄 重新生成高质量训练数据...")
        
        # 生成新数据
        train_texts, train_labels = self.create_training_data(train_size)
        valid_texts, valid_labels = self.create_training_data(valid_size)
        
        # 保存数据
        self.save_data(train_texts, train_labels, self.config.TRAIN_FILE)
        self.save_data(valid_texts, valid_labels, self.config.VALID_FILE)
        
        # 创建数据集
        tokenizer = AutoTokenizer.from_pretrained(self.config.MODEL_NAME)
        
        train_dataset = PunctuationDataset(train_texts, train_labels, tokenizer, self.config.MAX_LENGTH)
        valid_dataset = PunctuationDataset(valid_texts, valid_labels, tokenizer, self.config.MAX_LENGTH)
        
        train_loader = DataLoader(train_dataset, batch_size=self.config.BATCH_SIZE, shuffle=True)
        valid_loader = DataLoader(valid_dataset, batch_size=self.config.BATCH_SIZE, shuffle=False)
        
        return train_loader, valid_loader

if __name__ == "__main__":
    processor = ImprovedDataProcessor()
    train_loader, valid_loader = processor.prepare_datasets()
    print(f"训练数据批次数: {len(train_loader)}")
    print(f"验证数据批次数: {len(valid_loader)}")