import fitz  # PyMuPDF
import pandas as pd
import os
import tempfile
from paddleocr import PPStructureV3
from PIL import Image
import re
from io import StringIO
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

class AsyncPDFOCRParser:
    def __init__(self, pipeline_instance, ocr_lock):
        # 依赖外部传入的模型实例和共享锁
        if pipeline_instance is None or ocr_lock is None:
            raise ValueError("请先创建一个有效的模型实例，并传入共享锁")
        self.pipeline = pipeline_instance
        self.ocr_lock = ocr_lock

    # 对单个图片执行OCR
    def ocr_single_image(self, img_path: str):
        try:
            # 使用锁来保护对OCR模型的调用，确保线程安全
            with self.ocr_lock:           
                output = self.pipeline.predict(img_path)
            
            if not output:
                return []
            
            # 返回该页的解析结果列表
            return output[0].get('parsing_res_list', [])
        except Exception as e:
            print(f"错误：处理图片 {os.path.basename(img_path)} OCR失败: {e}")
            return []

    # 解析PDF文件，提取所有文本和表格
    def parse(self, file_path: str, max_workers: int = 4) -> str:
        all_text_fragments = []  # 用于存储所有文本片段
        temp_dir = tempfile.mkdtemp()  # 创建临时文件夹存放图片
        image_files_to_process = []  # 用于存储 (页码, 图片路径)

        try:
            # 1. 准备阶段
            # 一次性将所有PDF页面转换为图片，为并发处理做准备
            doc = fitz.open(file_path)
            print(f"文件 '{os.path.basename(file_path)}'：准备中，正在转换 {len(doc)} 页...")
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                mat = fitz.Matrix(2, 2)  # 将图片分辨率提高两倍，提高识别精度
                pix = page.get_pixmap(matrix=mat)  # 应用矩阵将页面转换为像素图像
                img = Image.frombytes('RGB', [pix.width, pix.height], pix.samples)  # 将像素图像转换为PIL可以处理的图像对象
                temp_img_path = os.path.join(temp_dir, f"page_{page_num}.png")
                img.save(temp_img_path)  # 将图像保存到临时文件夹中
                image_files_to_process.append((page_num, temp_img_path))
            
            doc.close()

            # 2. 并发处理阶段
            # 使用线程池并发执行OCR任务
            page_results = {} # 使用字典按页码存储结果，确保顺序
            print(f"文件 '{os.path.basename(file_path)}'：开始对 {len(image_files_to_process)} 个页面进行并行OCR...")
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_page = {
                    executor.submit(self.ocr_single_image, img_path): page_num 
                    for page_num, img_path in image_files_to_process
                }
                
                for future in as_completed(future_to_page):
                    page_num = future_to_page[future]
                    try:
                        page_results[page_num] = future.result()  # 获取该页的解析结果列表
                        # print(f"  - 第 {page_num + 1} 页完成") # 可以取消注释来查看详细进度
                    except Exception as e:
                        print(f"错误：获取第 {page_num + 1} 页的结果失败: {e}")
                        page_results[page_num] = []

            # 3. 结果整合阶段
            # 按页面顺序（0, 1, 2...）处理并拼接所有结果
            print(f"文件 '{os.path.basename(file_path)}'：所有页面处理完毕，正在合并内容...")
            TEXT_TYPES = {'text', 'doc_title', 'paragraph_title', 'number'}
            for page_num in sorted(page_results.keys()):
                page_content_fragments = []
                for block in page_results[page_num]:
                    # 如果block没有label或content属性，则跳过
                    if not hasattr(block, 'label') or not hasattr(block, 'content'):
                        continue

                    label = block.label.lower()  
                    content = block.content.strip()  

                    # 如果content为空，则跳过
                    if not content:
                        continue

                    if label in TEXT_TYPES:
                        # 使用正则表达式将字符串中多个连续的空格、换行、制表符等替换为一个空格
                        page_content_fragments.append(re.sub(r'\s+', ' ', content))
                    elif label == 'table':
                        try:
                            dfs = pd.read_html(StringIO(content))
                            for df in dfs:
                                if not df.empty:
                                    # 将DataFrame转换为格式化的纯文本（类似CSV）
                                    table_text = df.to_string(index=False, header=True)
                                    page_content_fragments.append(table_text)
                        except Exception as e:
                            print(f'警告：第 {page_num + 1} 页的表格转为文本时失败: {e}')
                
                if page_content_fragments:
                    # all_text_fragments 是列表，每一项为每一页的文本内容
                    all_text_fragments.append("\n".join(page_content_fragments))

            final_text = "\n\n".join(all_text_fragments)
            if not final_text.strip():
                raise ValueError("解析结果为空, 请检查PDF文件是否为空白文件")

            return final_text

        except Exception as e:
            print(f"严重错误：PDF解析流程中断: {e}")
            import traceback
            # 显示详细的错误报告
            traceback.print_exc()
            raise e
        finally:
            # 4. 清理阶段
            # 删除所有临时图片和文件夹
            for _, img_path in image_files_to_process:
                if os.path.exists(img_path):
                    os.remove(img_path)
            if os.path.exists(temp_dir):
                try: 
                    os.rmdir(temp_dir)
                except: 
                    pass