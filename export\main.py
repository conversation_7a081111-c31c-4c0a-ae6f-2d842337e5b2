# -*- coding: utf-8 -*-
"""
中文标点符号预测模型主程序 - 修正版
关键改动：
1. 兼容 config.py 新标签系统
2. 新增“清理旧模型”功能
"""

import sys
import os
import shutil
from collections import Counter
from trainer import PunctuationTrainer
from inference import main as inference_main
from improved_data_processor import ImprovedDataProcessor
from config import PunctuationConfig

def download_pretrained_model():
    """下载预训练模型"""
    print("\n📥 下载预训练模型...")
    try:
        from modelscope import snapshot_download
        config = PunctuationConfig()
        os.makedirs(config.DOWNLOADED_MODEL_DIR, exist_ok=True)
        model_dir = snapshot_download(
            config.PRETRAINED_MODEL_NAME,  # 使用 config 里的模型名
            cache_dir=config.DOWNLOADED_MODEL_DIR
        )
        print(f"✅ 模型已下载到: {model_dir}")
        return True
    except ImportError:
        print("❌ 缺少 modelscope 依赖，请运行: pip install modelscope")
        return False
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return False

def train_model():
    """训练模型"""
    print("\n🏋️ 开始训练模型...")
    try:
        trainer = PunctuationTrainer()
        best_f1 = trainer.train()
        print(f"\n🎉 训练完成！最佳 F1 分数: {best_f1:.4f}")
        return True
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        return False

def test_inference():
    """测试推理"""
    print("\n🔮 启动推理模块...")
    try:
        inference_main()
        return True
    except Exception as e:
        print(f"❌ 推理测试失败: {e}")
        return False

def generate_data():
    """生成训练数据"""
    print("\n📊 生成训练数据...")
    try:
        processor = ImprovedDataProcessor()
        num_train = int(input("训练数据量 (默认1000): ") or "1000")
        num_valid = int(input("验证数据量 (默认200): ") or "200")
        train_texts, train_labels = processor.create_training_data(num_train)
        valid_texts, valid_labels = processor.create_training_data(num_valid)
        processor.save_data(train_texts, train_labels, processor.config.TRAIN_FILE)
        processor.save_data(valid_texts, valid_labels, processor.config.VALID_FILE)
        print(f"✅ 数据生成完成！训练: {len(train_texts)} 条，验证: {len(valid_texts)} 条")
        return True
    except Exception as e:
        print(f"❌ 数据生成失败: {e}")
        return False

def analyze_data():
    """分析训练数据质量"""
    print("\n🔍 数据质量分析")
    config = PunctuationConfig()
    train_labels, _ = analyze_data_file(config.TRAIN_FILE)
    valid_labels, _ = analyze_data_file(config.VALID_FILE)
    if train_labels and valid_labels:
        total_train = sum(train_labels.values())
        total_valid = sum(valid_labels.values())
        if total_train:
            print(f"训练集非O标签比例: {(total_train - train_labels.get('O', 0)) / total_train * 100:.1f}%")
        if total_valid:
            print(f"验证集非O标签比例: {(total_valid - valid_labels.get('O', 0)) / total_valid * 100:.1f}%")
    return True

def analyze_data_file(filename):
    """分析单个数据文件"""
    print(f"\n📊 分析文件: {filename}")
    if not os.path.exists(filename):
        print(f"❌ 文件不存在: {filename}")
        return {}, []
    labels, sentences, current_sentence = [], [], []
    with open(filename, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                if current_sentence:
                    sentences.append(''.join([c for c, _ in current_sentence]))
                    labels.extend([l for _, l in current_sentence])
                    current_sentence = []
            else:
                parts = line.split('\t')
                if len(parts) == 2:
                    current_sentence.append(parts)
        if current_sentence:
            sentences.append(''.join([c for c, _ in current_sentence]))
            labels.extend([l for _, l in current_sentence])
    counter = Counter(labels)
    print(f"📈 总标签数: {len(labels)}，句子数: {len(sentences)}")
    config = PunctuationConfig()
    for label in config.LABELS:
        cnt = counter.get(label, 0)
        print(f"  {label} ({config.LABEL_TO_PUNCT.get(label, '')}): {cnt} ({cnt/len(labels)*100:.1f}%)" if labels else 0)
    return counter, sentences

def show_config():
    """显示配置信息"""
    print("\n⚙️ 当前配置:")
    config = PunctuationConfig()
    for k in ['PRETRAINED_MODEL_NAME', 'MODEL_NAME', 'MAX_LENGTH', 'BATCH_SIZE',
              'LEARNING_RATE', 'NUM_EPOCHS', 'DEVICE', 'LABELS', 'OUTPUT_DIR']:
        print(f"{k}: {getattr(config, k)}")
    print(f"预训练模型路径: {config.get_pretrained_model_path()}")
    print(f"训练模型路径: {config.get_training_model_path()}")

def compare_models():
    """模型对比演示"""
    print("\n🔄 启动模型对比演示...")
    try:
        from inference import PunctuationComparisonPredictor
        comparator = PunctuationComparisonPredictor()
        config = PunctuationConfig()
        valid_file = config.VALID_FILE
        test_sentences = []
        if os.path.exists(valid_file):
            with open(valid_file, 'r', encoding='utf-8') as f:
                current = []
                for line in f:
                    line = line.strip()
                    if line == "":
                        if current:
                            test_sentences.append(''.join([c.split('\t')[0] for c in current]))
                            current = []
                    else:
                        current.append(line)
                if current:
                    test_sentences.append(''.join([c.split('\t')[0] for c in current]))
        else:
            test_sentences = ["今天天气很好我们去公园玩吧"] * 5
        comparator.print_comparison(test_sentences)
        while True:
            user = input("\n请输入文本对比（back返回）: ").strip()
            if user.lower() in ['back', '返回', 'quit', 'exit']:
                break
            if user:
                comparator.print_comparison([user])
        return True
    except Exception as e:
        print(f"❌ 模型对比失败: {e}")
        return False

def clear_old_model():
    """清理旧模型"""
    print("\n🧹 清理旧模型...")
    config = PunctuationConfig()
    model_path = config.get_training_model_path()
    if os.path.exists(model_path):
        shutil.rmtree(model_path)
        print(f"✅ 已删除旧模型目录: {model_path}")
    else:
        print("ℹ️ 未找到旧模型目录，无需清理。")

def check_environment():
    """检查环境"""
    print("🔍 检查环境...")
    required = ['torch', 'transformers', 'sklearn', 'numpy', 'tqdm']
    missing = [p for p in required if not __import__(p, fromlist=[''])]
    if missing:
        print(f"❌ 缺少依赖: {', '.join(missing)}")
        print("请运行: pip install torch transformers scikit-learn numpy tqdm")
        return False
    print("✅ 所有依赖已安装")
    PunctuationConfig.create_dirs()
    print("✅ 目录结构已创建")
    return True

def show_menu():
    """显示菜单"""
    print("\n" + "=" * 50)
    print("🚀 中文标点符号预测模型")
    print("=" * 50)
    print("1. 下载预训练模型")
    print("2. 训练模型")
    print("3. 测试推理")
    print("4. 生成数据")
    print("5. 分析数据")
    print("6. 查看配置")
    print("7. 模型对比演示")
    print("8. 退出")
    print("9. 清理旧模型")  # 新增
    print("-" * 50)

def main():
    if not check_environment():
        return
    while True:
        show_menu()
        try:
            choice = input("请选择操作 (1-9): ").strip()
            if choice == '1':
                download_pretrained_model()
            elif choice == '2':
                train_model()
            elif choice == '3':
                test_inference()
            elif choice == '4':
                generate_data()
            elif choice == '5':
                analyze_data()
            elif choice == '6':
                show_config()
            elif choice == '7':
                compare_models()
            elif choice == '8':
                print("👋 再见！")
                break
            elif choice == '9':
                clear_old_model()
            else:
                print("❌ 无效选择，请输入 1-9")
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，再见！")
            break
        except Exception as e:
            print(f"❌ 程序出错: {e}")

if __name__ == "__main__":
    main()
