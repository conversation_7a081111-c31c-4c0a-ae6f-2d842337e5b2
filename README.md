# 中文标点符号预测模型

基于阿里达摩院 CT-Transformer 的中文标点符号预测系统，支持训练和推理功能。

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install funasr torch
```

### 2. 运行程序

```bash
# Windows环境下使用完整路径
E:/Anaconda/envs/bd/python.exe export/main.py

# 或者直接运行（如果环境配置正确）
python export/main.py
```

### 3. 使用流程

1. **训练模型** - 选择菜单选项 2（会自动下载 CT-Transformer 模型）
2. **测试推理** - 选择菜单选项 3
3. **生成数据** - 选择菜单选项 4（可选）
4. **分析数据** - 选择菜单选项 5（可选）

## 📋 功能特性

- ✅ 基于官方 CT-Transformer 预训练模型
- ✅ 支持模型下载和管理
- ✅ 智能数据生成和分析
- ✅ 完整的训练和推理流程
- ✅ 中文界面和详细指导

## 📁 项目结构

```
ct_transformer/
├── export/
│   ├── main.py                    # 主程序入口
│   ├── config.py                  # 配置文件
│   ├── trainer.py                 # 训练模块
│   ├── inference.py               # 推理模块
│   ├── improved_data_processor.py # 数据处理模块
│   ├── download.py                # 模型下载脚本
│   └── test_basic.py              # 基础功能测试
├── model/                         # 下载的模型目录
├── data/                          # 训练数据目录
├── output/                        # 输出目录
├── 调试手册.md                    # 详细使用手册
└── README.md                      # 本文件
```

## 🔧 主要改进

1. **模型集成** - 使用官方 CT-Transformer 模型，通过 FunASR 框架加载
2. **功能整合** - 将数据分析功能集成到主程序
3. **智能路径管理** - 自动管理预训练和训练后模型路径
4. **容错机制** - 添加多层级的模型加载容错
5. **中文指导** - 完整的中文使用手册和错误提示
6. **性能优化** - 直接使用 CT-Transformer 进行推理，无需额外训练

## 📖 详细文档

请查看 [调试手册.md](调试手册.md) 获取详细的使用说明、常见问题解决方案和性能优化建议。

## 🎯 使用示例

```python
# 推理示例
from export.inference import PunctuationPredictor

predictor = PunctuationPredictor()
result = predictor.predict("今天天气很好我们去公园玩吧")
print(result)  # 输出: 今天天气很好，我们去公园玩吧。
```

## ⚠️ 注意事项

- 首次使用需要下载预训练模型（约 1-2GB）
- 需要安装 funasr 依赖：`pip install funasr torch`
- Windows 环境下建议使用完整 Python 路径运行
- 确保网络连接正常以下载模型

## 🆘 技术支持

如遇问题，请：

1. 查看 [调试手册.md](调试手册.md) 中的常见问题部分
2. 运行 `python export/test_basic.py` 检查环境
3. 确认所有依赖已正确安装
