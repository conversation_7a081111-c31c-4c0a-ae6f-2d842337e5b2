# -*- coding: utf-8 -*-
"""
中文标点符号预测模型配置文件（已统一标签顺序）
"""
import os
import torch

class PunctuationConfig:
    """标点符号预测模型配置"""

    # 模型配置 - 统一使用CT-Transformer
    PRETRAINED_MODEL_NAME = "damo/punc_ct-transformer_zh-cn-common-vocab272727-pytorch"
    MODEL_NAME = PRETRAINED_MODEL_NAME
    MODEL_REVISION = "v2.0.4"

    MAX_LENGTH = 512

    # 统一标签顺序（非常重要：训练/推理必须一致）
    LABELS = [
        'O',        # 无标点
        'COMMA',    # 逗号 ，
        'PAUSE',    # 顿号 、  （注意：PAUSE 放在 COMMA 之后以保证ID一致）
        'PERIOD',   # 句号 。
        'QUESTION', # 问号 ？
        'EXCLAIM',  # 感叹号 ！
    ]

    # 标点符号映射
    LABEL_TO_PUNCT = {
        'O': '',
        'COMMA': '，',
        'PAUSE': '、',
        'PERIOD': '。',
        'QUESTION': '？',
        'EXCLAIM': '！',
    }

    # 标签与ID映射（自动生成，确保一致）
    LABEL_TO_ID = {label: i for i, label in enumerate(LABELS)}
    ID_TO_LABEL = {i: label for i, label in enumerate(LABELS)}

    # 训练配置 - 优化后的高效训练参数
    BATCH_SIZE = 64  # 提高批次大小，充分利用RTX 4090显存
    LEARNING_RATE = 2e-5  # 提高学习率，加快收敛速度
    NUM_EPOCHS = 50  # 减少训练轮数，但提高每轮效率
    WEIGHT_DECAY = 0.01
    WARMUP_RATIO = 0.1
    GRADIENT_ACCUMULATION_STEPS = 2  # 减少梯度累积步数，加快训练速度
    MAX_GRAD_NORM = 1.0  # 梯度裁剪

    # 新增优化参数
    USE_FOCAL_LOSS = True  # 启用Focal Loss处理类别不平衡
    FOCAL_ALPHA = 0.25  # Focal Loss alpha参数
    FOCAL_GAMMA = 2.0  # Focal Loss gamma参数
    PRECOMPUTE_CT_FEATURES = True  # 启用CT特征预计算
    ENHANCED_DROPOUT = 0.3  # 增强的dropout率防止过拟合
    USE_CT_ENCODER_FEATURES = True  # 直接使用CT-Transformer编码器隐层特征（方案B）
    CT_ENCODER_HOOK_MODULE = ""     # 指定要挂钩的编码器模块名（可留空，自动探测）

    # 路径配置
    DATA_DIR = './data'
    OUTPUT_DIR = './output'
    MODEL_SAVE_DIR = os.path.join(OUTPUT_DIR, 'model')
    LOG_DIR = os.path.join(OUTPUT_DIR, 'logs')

    DOWNLOADED_MODEL_DIR = './model/downloaded_model'
    MODEL_PATH_FILE = './export/model_path.txt'

    FINETUNED_MODEL_DIR = os.path.join(MODEL_SAVE_DIR, 'finetuned')
    FINETUNED_MODEL_CONFIG = os.path.join(FINETUNED_MODEL_DIR, 'config.json')
    FINETUNED_MODEL_WEIGHTS = os.path.join(FINETUNED_MODEL_DIR, 'pytorch_model.bin')
    TRAINING_ARGS_FILE = os.path.join(FINETUNED_MODEL_DIR, 'training_args.json')

    TRAIN_FILE = os.path.join(DATA_DIR, 'train.txt')
    VALID_FILE = os.path.join(DATA_DIR, 'valid.txt')
    TEST_FILE = os.path.join(DATA_DIR, 'test.txt')

    DEVICE = 'cuda' if torch.cuda.is_available() else 'cpu'

    @classmethod
    def create_dirs(cls):
        dirs_to_create = [
            cls.DATA_DIR,
            cls.OUTPUT_DIR,
            cls.MODEL_SAVE_DIR,
            cls.LOG_DIR,
            cls.DOWNLOADED_MODEL_DIR,
            cls.FINETUNED_MODEL_DIR,
            os.path.join(os.getcwd(), "models", "pretrained")
        ]
        for dir_path in dirs_to_create:
            cls._ensure_dir_exists(dir_path)

    @classmethod
    def _ensure_dir_exists(cls, dir_path):
        if not os.path.exists(dir_path):
            try:
                os.makedirs(dir_path, exist_ok=True)
                print(f"📁 创建目录: {dir_path}")
            except OSError as e:
                print(f"❌ 创建目录失败 {dir_path}: {e}")
                raise
        else:
            # 不总是打印，避免日志过多
            pass

    @classmethod
    def get_pretrained_cache_dir(cls):
        return os.path.join(os.getcwd(), "models", "pretrained")

    @classmethod
    def clean_temp_files(cls):
        temp_patterns = ["*.tmp", "*.temp", "__pycache__", "*.pyc"]
        import glob, shutil
        for pattern in temp_patterns:
            for file_path in glob.glob(pattern, recursive=True):
                try:
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                    elif os.path.isdir(file_path):
                        shutil.rmtree(file_path)
                except Exception:
                    pass

    @classmethod
    def get_pretrained_model_path(cls):
        try:
            if os.path.exists(cls.MODEL_PATH_FILE):
                with open(cls.MODEL_PATH_FILE, 'r', encoding='utf-8') as f:
                    model_path = f.read().strip()
                    if model_path and os.path.exists(model_path):
                        return model_path
        except Exception:
            pass
        return cls.PRETRAINED_MODEL_NAME

    @classmethod
    def has_finetuned_model(cls):
        return (os.path.exists(cls.FINETUNED_MODEL_CONFIG) and
                os.path.exists(cls.FINETUNED_MODEL_WEIGHTS))

    @classmethod
    def get_inference_model_path(cls):
        if cls.has_finetuned_model():
            return cls.FINETUNED_MODEL_DIR
        return cls.get_pretrained_model_path()

    @classmethod
    def get_training_model_path(cls):
        return cls.get_inference_model_path()

    @classmethod
    def save_model_path(cls, model_path):
        try:
            model_path_dir = os.path.dirname(cls.MODEL_PATH_FILE)
            if model_path_dir:
                cls._ensure_dir_exists(model_path_dir)
            with open(cls.MODEL_PATH_FILE, 'w', encoding='utf-8') as f:
                f.write(model_path)
            print(f"📝 模型路径已保存: {model_path}")
        except Exception as e:
            print(f"⚠️ 保存模型路径失败: {e}")

    @classmethod
    def ensure_model_path_file(cls):
        if not os.path.exists(cls.MODEL_PATH_FILE):
            try:
                model_path_dir = os.path.dirname(cls.MODEL_PATH_FILE)
                if model_path_dir:
                    cls._ensure_dir_exists(model_path_dir)
                with open(cls.MODEL_PATH_FILE, 'w', encoding='utf-8') as f:
                    f.write("")
            except Exception as e:
                print(f"⚠️ 创建模型路径文件失败: {e}")

# 初始化目录
PunctuationConfig.create_dirs()
PunctuationConfig.ensure_model_path_file()
