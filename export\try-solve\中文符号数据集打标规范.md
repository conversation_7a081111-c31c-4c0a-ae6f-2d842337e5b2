# 中文标点符号预测模型打标规范手册

## 1. 标签体系

### 1.1 标签定义

本项目采用 6 类标签体系：

| 标签     | 符号 | 含义   | 使用场景                 |
| -------- | ---- | ------ | ------------------------ |
| O        | 无   | 无标点 | 字符后不需要添加标点符号 |
| COMMA    | ，   | 逗号   | 句子内部停顿、并列关系   |
| PAUSE    | 、   | 顿号   | 并列词语间的停顿         |
| PERIOD   | 。   | 句号   | 陈述句结尾               |
| QUESTION | ？   | 问号   | 疑问句结尾               |
| EXCLAIM  | ！   | 感叹号 | 感叹句、强调句结尾       |

### 1.2 标注格式

采用**字符级标注**，格式为：

```
字符\t标签
```

示例：

```
我	O
今	O
天	O
很	O
高	COMMA
兴	O
大	O
家	O
一	O
起	O
学	O
习	PERIOD
```

## 2. 详细打标规则

### 2.1 顿号（PAUSE）标注规则

#### 2.1.1 并列词语间的停顿

**基本原则**：顿号用于分隔句子内部性质相同的并列成分，停顿短于逗号。

- **并列名词**：在每个并列成分的最后一个字符后标注（最后一项除外）

```
苹	O
果	PAUSE  ← 第一个并列项后
香	O
蕉	PAUSE  ← 第二个并列项后
橙	O
子	O      ← 最后一项不加顿号
都	O
很	O
好	O
吃	PERIOD
```

- **并列形容词**：形容词并列修饰时使用

```
美	O
丽	PAUSE  ← 第一个形容词后
大	O
方	PAUSE  ← 第二个形容词后
温	O
柔	O      ← 最后一个形容词不加顿号
的	O
女	O
孩	PERIOD
```

- **并列动词短语**：简短的动作并列

```
跑	O
步	PAUSE  ← 第一个动作后
游	O
泳	PAUSE  ← 第二个动作后
打	O
球	O      ← 最后一个动作不加顿号
是	O
他	O
的	O
爱	O
好	PERIOD
```

#### 2.1.2 并列数词和量词

```
一	O
二	PAUSE
三	PAUSE
四	O
五	O
名	O
学	O
生	PERIOD

三	O
个	PAUSE
五	O
个	PAUSE
八	O
个	O
苹	O
果	PERIOD
```

#### 2.1.3 并列时间和地点

- **时间并列**：

```
周	O
一	PAUSE
周	O
三	PAUSE
周	O
五	O
都	O
有	O
课	PERIOD
```

- **地点并列**：

```
北	O
京	PAUSE
上	O
海	PAUSE
广	O
州	O
是	O
大	O
城	O
市	PERIOD
```

#### 2.1.4 顿号与逗号的区分

- **顿号**：用于短小的、性质相同的并列成分
- **逗号**：用于较长的并列成分，或不同性质成分的分隔

**错误示例**（应用顿号）：

```
他	O
喜	O
欢	O
苹	O
果	COMMA  ← 错误：短并列应用顿号
香	O
蕉	COMMA  ← 错误：短并列应用顿号
橙	O
子	PERIOD
```

**正确示例**：

```
他	O
喜	O
欢	O
苹	O
果	PAUSE  ← 正确：短并列用顿号
香	O
蕉	PAUSE  ← 正确：短并列用顿号
橙	O
子	PERIOD
```

### 2.2 逗号（COMMA）标注规则

#### 2.2.1 并列关系

- **长并列句子**：在第一个分句末尾标注

```
今	O
天	O
天	O
气	O
很	O
好	COMMA  ← 分句停顿
我	O
们	O
去	O
郊	O
游	PERIOD
```

- **复杂并列成分**：当并列成分较长或内部还有修饰语时

```
努	O
力	O
学	O
习	O
的	O
小	O
明	COMMA  ← 较长并列成分
认	O
真	O
工	O
作	O
的	O
小	O
红	COMMA  ← 较长并列成分
都	O
是	O
好	O
学	O
生	PERIOD
```

#### 2.2.2 转折和递进

- **转折连词前**：但是、可是、不过等前

```
他	O
很	O
努	O
力	COMMA  ← 转折停顿
但	O
是	O
成	O
绩	O
不	O
好	PERIOD
```

- **递进连词前**：而且、另外、此外等前

```
这	O
个	O
方	O
案	O
很	O
好	COMMA  ← 递进停顿
而	O
且	O
可	O
行	O
性	O
高	PERIOD
```

#### 2.2.3 条件和假设

- **条件从句后**：如果...、假如...等后

```
如	O
果	O
明	O
天	O
下	O
雨	COMMA  ← 条件停顿
我	O
们	O
就	O
不	O
去	O
了	PERIOD
```

#### 2.2.4 时间和地点状语后

```
在	O
学	O
校	O
里	COMMA  ← 地点状语停顿
我	O
们	O
认	O
真	O
学	O
习	PERIOD

昨	O
天	O
晚	O
上	COMMA  ← 时间状语停顿
我	O
看	O
了	O
一	O
本	O
书	PERIOD
```

### 2.3 句号（PERIOD）标注规则

#### 2.3.1 陈述句结尾

- **普通陈述**：表达一个完整的意思

```
我	O
喜	O
欢	O
读	O
书	PERIOD
```

- **说明解释**：对事物的说明或解释

```
这	O
是	O
一	O
本	O
好	O
书	PERIOD
```

#### 2.3.2 命令句结尾（温和语气）

```
请	O
你	O
帮	O
我	O
一	O
下	PERIOD
```

### 2.4 问号（QUESTION）标注规则

#### 2.4.1 疑问句类型

**一般疑问句**：

```
你	O
好	O
吗	QUESTION
```

**特殊疑问句**：

```
你	O
叫	O
什	O
么	O
名	O
字	QUESTION
```

**选择疑问句**：

```
你	O
喜	O
欢	O
苹	O
果	O
还	O
是	O
香	O
蕉	QUESTION
```

**反问句**：

```
这	O
样	O
做	O
不	O
对	O
吧	QUESTION
```

#### 2.4.2 疑问词标识

常见疑问词：吗、呢、吧、如何、怎么、什么、哪里、谁等

### 2.5 感叹号（EXCLAIM）标注规则

#### 2.5.1 感叹句

- **情感强烈**：表达惊讶、高兴、愤怒等强烈情感

```
太	O
棒	O
了	EXCLAIM
```

- **赞叹**：对事物的赞美

```
多	O
么	O
美	O
丽	O
的	O
风	O
景	EXCLAIM
```

#### 2.5.2 命令句（强烈语气）

```
快	O
点	O
跑	EXCLAIM
```

#### 2.5.3 呼唤

```
小	O
明	EXCLAIM
```

### 2.6 无标点（O）标注规则

#### 2.6.1 句子内部字符

除了需要标点的位置外，其他所有字符都标注为 O：

```
我	O  ← 句子开头
今	O  ← 句子中间
天	O  ← 句子中间
很	O  ← 句子中间
开	O  ← 句子中间，最后一个字符标注句末标点
心	PERIOD
```

## 3. 特殊情况处理

### 3.1 顿号的特殊用法

#### 3.1.1 顿号与"和"、"与"、"及"的配合

当最后两项用"和"、"与"、"及"连接时，前面仍用顿号：

```
苹	O
果	PAUSE
香	O
蕉	PAUSE
橙	O
子	O
和	O
葡	O
萄	PERIOD
```

#### 3.1.2 不用顿号的情况

- **两项并列**：只有两个并列成分时，直接用"和"连接，不用顿号

```
苹	O
果	O
和	O
香	O
蕉	PERIOD
```

- **层次不同的并列**：并列成分内部层次不同时，用不同级别的标点

```
文	O
学	PAUSE      ← 第一层并列
历	O
史	O
和	O
哲	O
学	COMMA      ← 第二层并列，较大停顿
数	O
学	PAUSE      ← 第一层并列
物	O
理	O
和	O
化	O
学	O
都	O
很	O
重	O
要	PERIOD
```

### 3.2 复合句标注

对于复杂的复合句，按照语意停顿进行标注：

```
虽	O
然	O
天	O
气	O
不	O
好	COMMA  ← 让步从句后停顿
但	O
是	O
我	O
们	O
还	O
是	O
要	O
出	O
发	PERIOD
```

### 3.3 引用和对话

暂不考虑引号，只标注语句本身的标点：

```
他	O
说	COMMA  ← 说话动作后停顿
今	O
天	O
要	O
下	O
雨	PERIOD
```

### 3.4 数字和专有名词

数字、专有名词内部不加标点：

```
二	O
零	O
二	O
四	O
年	O
是	O
好	O
年	PERIOD
```

### 3.5 网址、邮箱等特殊格式

保持原格式，不添加额外标点：

```
请	O
访	O
问	O
我	O
们	O
的	O
网	O
站	PERIOD
```

## 4. 标注流程

### 4.1 预处理

1. **文本清理**：去除原有标点符号
2. **分句**：按照语意将长文本分成短句
3. **字符分割**：将句子分割成单个字符

### 4.2 标注步骤

#### Step 1: 识别句子类型

- 陈述句 → 考虑句号
- 疑问句 → 考虑问号
- 感叹句 → 考虑感叹号
- 祈使句 → 根据语气选择句号或感叹号

#### Step 2: 识别并列关系

- 短小并列成分 → 考虑顿号
- 较长并列成分 → 考虑逗号
- 区分并列层次 → 选择合适标点

#### Step 3: 识别句内停顿

- 主谓宾结构中的停顿
- 状语后的停顿
- 从句间的停顿
- 并列成分间的停顿

#### Step 4: 逐字标注

- 句末字符：标注相应的句末标点
- 并列停顿：根据成分长短选择顿号或逗号
- 语法停顿：标注逗号
- 其他位置：标注 O

### 4.3 质量检查

#### 4.3.1 标注一致性检查

- **同类句式**：相似句式的标注应保持一致
- **语法规则**：符合中文语法习惯
- **标点层次**：顿号、逗号使用层次合理
- **标点比例**：检查各标点符号的比例是否合理

#### 4.3.2 常见错误检查

- **遗漏句末标点**：每个句子必须有句末标点
- **顿号逗号混淆**：区分短并列（顿号）和长并列（逗号）
- **过度标注**：避免在不需要停顿的地方加标点
- **标点混淆**：区分问号和感叹号的使用场景

## 5. 标注示例（仅用于程序员后续处理 xls 为 txt，标注员无需关注）

### 5.1 完整句子标注示例

**例句 1**（含顿号的陈述句）：
原文："苹果香蕉橙子都是水果"

```
苹	O
果	PAUSE
香	O
蕉	PAUSE
橙	O
子	O
都	O
是	O
水	O
果	PERIOD
```

**例句 2**（疑问句）：
原文："你觉得这个方案怎么样"

```
你	O
觉	O
得	O
这	O
个	O
方	O
案	O
怎	O
么	O
样	QUESTION
```

**例句 3**（感叹句）：
原文："多么美好的一天啊"

```
多	O
么	O
美	O
好	O
的	O
一	O
天	O
啊	EXCLAIM
```

**例句 4**（复合句含顿号）：
原文："如果明天天气好我们就去爬山游泳钓鱼"

```
如	O
果	O
明	O
天	O
天	O
气	O
好	COMMA
我	O
们	O
就	O
去	O
爬	O
山	PAUSE
游	O
泳	PAUSE
钓	O
鱼	PERIOD
```

**例句 5**（层次并列）：
原文："语文数学都很重要英语物理也不能忽视"

```
语	O
文	PAUSE
数	O
学	O
都	O
很	O
重	O
要	COMMA
英	O
语	PAUSE
物	O
理	O
也	O
不	O
能	O
忽	O
视	PERIOD
```

### 5.2 批量标注格式示例

```
# 文件格式：train.txt
我	O
今	O
天	O
很	O
开	O
心	PERIOD

你	O
好	O
吗	QUESTION

太	O
棒	O
了	EXCLAIM

苹	O
果	PAUSE
香	O
蕉	PAUSE
橙	O
子	PERIOD
```

## 6. 标注工具建议

### 6.1 推荐标注工具

- **LabelStudio**：开源数据标注平台
- **doccano**：轻量级文本标注工具
- **自定义工具**：基于项目需求开发简单标注界面

### 6.2 标注效率提升

- **预标注**：使用现有模型进行预标注，人工修正
- **规则辅助**：使用正则表达式识别明显的并列结构
- **批量操作**：相似句式批量应用标注规则
- **快捷键**：设置快捷键提高标注速度（建议：O→1, COMMA→2, PAUSE→3, PERIOD→4, QUESTION→5, EXCLAIM→6）

## 7. 质量控制

### 7.1 标注质量指标

- **一致性**：同一标注员前后标注的一致性 ≥ 95%
- **准确性**：标注结果符合语法规则 ≥ 98%
- **完整性**：所有字符都有对应标签 = 100%

### 7.2 多人标注流程

1. **独立标注**：多个标注员独立标注同一批数据
2. **一致性检查**：计算标注员间一致性
3. **争议解决**：讨论解决标注差异，特别是顿号与逗号的选择
4. **最终确认**：形成最终标注结果

### 7.3 标注数据统计

建议的标签分布（参考比例）：

- **O 标签**：75-80%（大部分字符）
- **PERIOD**：8-12%（句号最常见）
- **COMMA**：4-7%（逗号次之）
- **PAUSE**：2-4%（顿号使用频率中等）
- **QUESTION**：1-3%（问号较少）
- **EXCLAIM**：1-2%（感叹号最少）

## 8. 常见问题解答

### Q1：顿号和逗号如何区分？

**A1**：

- **顿号**：用于短小的、性质相同的词语并列，如：苹果、香蕉、橙子
- **逗号**：用于较长的短语并列，或分句间停顿，如：努力学习的小明，认真工作的小红

### Q2：什么时候不用顿号？

**A2**：

- 只有两个并列成分时：苹果和香蕉（不用顿号）
- 并列成分中有"和"、"与"、"及"时：语文、数学和英语（最后一项前不用顿号）

### Q3：复合句中多个停顿如何处理？

**A3**：按照语意和停顿长短选择：短停顿用顿号，中停顿用逗号，长停顿考虑分句。

### Q4：专有名词内部是否需要标点？

**A4**：专有名词内部不加标点，作为整体处理。

### Q5：标注数据量建议？

**A5**：

- **最少训练数据**：15,000 句（增加顿号后数据需求略增）
- **推荐训练数据**：60,000-120,000 句
- **验证数据**：训练数据的 10-20%
- **测试数据**：验证数据同等规模

### Q6：如何处理顿号的层次问题？

**A6**：

- **同一层次**：统一使用顿号
- **不同层次**：大层次用逗号，小层次用顿号
- 例如：文学、历史，数学、物理（文史为一类，数理为一类）

---

**版本**: v2.0  
**更新日期**: 2025 年 8 月  
**适用项目**: 中文标点符号预测模型（含顿号）  
**维护者**: zjj  
**主要更新**: 添加顿号（PAUSE）标注规范，完善标注体系
