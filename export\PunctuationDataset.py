# -*- coding: utf-8 -*-
"""
终极容错版：先规范化 annotated，再对齐
"""
import argparse
import os
import re
import random
import pandas as pd
from typing import List, Tuple

LABEL_MAP = {
    '，': 'COMMA',
    '、': 'PAUSE',
    '。': 'PERIOD',
    '？': 'QUESTION',
    '！': 'EXCLAIM',
}
PUNCT_SET = set(LABEL_MAP.keys())

# ====== 1. 正则：所有要移除的标点 ======
PUNCT_RE = re.compile(r'[，。？！、；：“”‘’""''\.\?\!,\;:]')

def normalize(text: str) -> str:
    """去掉空格与所有标点"""
    return PUNCT_RE.sub('', text.replace(' ', ''))

def squash_punct(text: str) -> str:
    """合并连续相同标点，例如 ,, -> ,"""
    for p in PUNCT_SET:
        text = re.sub(f'{re.escape(p)}+', p, text)
    return text

def align_labels(annotated: str) -> Tuple[str, List[str]]:
    """
    以 annotated 为基准，返回 (干净原文, 标签)
    容错：空格/连续标点/全角半角
    """
    annotated = squash_punct(annotated)           # 合并连续相同标点
    clean = normalize(annotated)                  # 得到干净原文
    labels = ['O'] * len(clean)

    clean_idx = ann_idx = 0
    while ann_idx < len(annotated):
        ch = annotated[ann_idx]
        if ch in PUNCT_SET:
            # 标点映射到前一个字符
            if clean_idx > 0:
                labels[clean_idx - 1] = LABEL_MAP[ch]
            ann_idx += 1
        elif ch == ' ':
            ann_idx += 1        # 跳过空格
        else:
            # 普通字符
            if clean_idx < len(clean) and ch == clean[clean_idx]:
                clean_idx += 1
                ann_idx += 1
            else:
                # 其余字符不匹配 -> 报错
                raise ValueError(
                    f"字符无法对齐：原文期望 '{clean[clean_idx] if clean_idx < len(clean) else 'EOF'}' "
                    f"但标注出现 '{ch}'\n完整标注：{annotated}"
                )
    return clean, labels

def write_txt(rows: List[str], path: str):
    os.makedirs(os.path.dirname(path), exist_ok=True)
    with open(path, 'w', encoding='utf-8') as f:
        for annotated in rows:
            text, labels = align_labels(annotated)
            for ch, lbl in zip(text, labels):
                f.write(f"{ch}\t{lbl}\n")
            f.write("\n")

def convert_xls_to_txt(xls_path: str, output_dir: str, train_ratio: float = 0.8):
    df = pd.read_excel(xls_path, engine='xlrd')
    if len(df.columns) < 2:
        raise ValueError("xls 必须包含两列：B 列=已标点文本")

    annotated_col = df.columns[1]
    rows = [
        str(row[annotated_col]).strip()
        for _, row in df.iterrows()
        if str(row[annotated_col]).strip()
    ]

    random.shuffle(rows)
    split_idx = int(len(rows) * train_ratio)
    train_rows, valid_rows = rows[:split_idx], rows[split_idx:]

    write_txt(train_rows, os.path.join(output_dir, 'train.txt'))
    write_txt(valid_rows, os.path.join(output_dir, 'valid.txt'))

    print(f"✅ 转换完成！")
    print(f"   总样本：{len(rows)}")
    print(f"   训练集：{len(train_rows)} → {os.path.join(output_dir, 'train.txt')}")
    print(f"   验证集：{len(valid_rows)} → {os.path.join(output_dir, 'valid.txt')}")

def main():
    parser = argparse.ArgumentParser(description="终极容错版 PunctuationDataset")
    parser.add_argument("--input", required=True, help="已标点 xls")
    parser.add_argument("--output_dir", default="./data", help="输出目录")
    parser.add_argument("--train_ratio", type=float, default=0.8, help="训练集比例")
    args = parser.parse_args()
    convert_xls_to_txt(args.input, args.output_dir, args.train_ratio)

if __name__ == "__main__":
    main()