# 中文标点符号预测模型 - 调试手册（修复版）

## 🚨 重要更新：日志错误修复与代码优化

- 更新日期：2025-08-11
- 修复状态：✅ 已完成
- 影响范围：export/inference.py、export/trainer.py、调试手册

### 一、问题原因分析：ValueError: I/O operation on closed file

- 现象：在菜单“7. 模型对比演示”或训练过程中，外部库（funasr / modelscope / huggingface_hub）在下载/加载模型阶段通过 logging 输出日志时，抛出 `ValueError: I/O operation on closed file`。
- 触发点：logging.Handler 的底层流对象（例如 StreamHandler 的 stream）被关闭或失效，emit() 写入该流时触发异常。
- 代码线索：调用栈显示问题源自 funasr 的模型拉取/下载阶段写日志，项目自身未显式调用 logging.shutdown，但存在多处 basicConfig 与可能的处理器重复配置，使问题更易暴露。

### 二、具体修复步骤（统一的防御性日志机制）

1. 在 inference.py 与 trainer.py 顶部统一日志初始化：
   - 使用 `logging.basicConfig(level=logging.INFO)`
   - 设置 `logging.raiseExceptions = False` 避免日志内部异常外抛
   - 为当前模块 logger 添加独立的 `StreamHandler(sys.stdout)`，并设置 `logger.propagate = False`
2. 新增工具函数 `_ensure_logging_handlers_open()`：
   - 遍历根 logger 以及外部库 logger（"", "funasr", "modelscope", "huggingface_hub"）
   - 移除已关闭/无效流的 handler；若根 logger 空则补充一个 `StreamHandler(sys.stderr)`
   - 将相关 logger 设为可用，并允许外部库 logger 传播
3. 在关键入口调用修复器：
   - `PunctuationPredictor.__init__`、`_load_pretrained_model()`、`_load_ct_based_model()`
   - `PunctuationTrainer.__init__` 在最开始与外部库调用前各调用一次

以上仅调整日志配置与容错逻辑，不改变任何业务流程。

### 三、代码优化要点（保持功能与输出不变）

- 清理 trainer.py 中一次性变量 `result`（未使用），避免无意义赋值。
- 保持所有现有日志内容与输出格式不变，仅增强日志系统稳定性；不调整训练流程、指标计算、保存策略等。
- 统一日志配置，减少多处 basicConfig 带来的处理器重复添加风险。

### 四、预防类似问题的建议

- 避免在运行中显式关闭 logging handler 的底层 stream；不要在库代码外部调用 `logging.shutdown()`。
- 在可能由第三方库发起大量日志输出的入口处，调用 `_ensure_logging_handlers_open()` 做健康检查。
- 模块内的 logger 使用独立的 handler，并设置 `propagate=False`，避免重复输出与外部处理器干扰。
- 将 `logging.raiseExceptions = False` 作为生产环境默认设置，防止日志异常影响主流程。

---

## 标点符号集合不一致修复（CT-Transformer 与项目配置对齐）

### 问题发现过程

- 在训练日志中发现 CT-Transformer 提取的 punc*list = ['<unk>', '*', '，', '。', '？', '、']，缺少感叹号 '！'。
- 本项目配置的标点集合为：逗号(，)、顿号(、)、句号(。)、问号(？)、感叹号(！)。
- 两者不一致会导致 tokenizer 对 '！' 的编码不可控（落入默认字符映射），从而在训练中对该标点的语义表达不稳定。

### 影响分析

- 训练标签仍按项目配置 ['O','COMMA','PAUSE','PERIOD','QUESTION','EXCLAIM'] 生成，不受 punc_list 影响；但输入侧如果对 '！' 缺少稳定、可区分的编码，模型学习到的 'EXCLAIM' 类别会变弱，可能影响召回和精度。
- 推理阶段同理，若 tokenizer 未对 '！' 明确建模，'！' 出现时的特征不稳定，影响预测决策边界。

### 具体修正方案

- 在 export/trainer.py 的 `_extract_ct_tokenizer()` 路径中，如果读取到 CT-Transformer 的 `punc_list`，则调用 `_create_ct_tokenizer_from_punc_list(punc_list)` 创建 tokenizer。
- 本次对 `_create_ct_tokenizer_from_punc_list` 进行了增强：
  1. 依据 config.LABELS 和 LABEL_TO_PUNCT 生成目标标点序列（排除 'O'），确保包含并按标签顺序排列 ['，','、','。','？','！']；
  2. 在保持上述目标序列优先的基础上，补充 `punc_list` 中其他标点（剔除占位符 '<unk>' 和 '\_'），生成 `normalized_puncs`；
  3. 使用 `normalized_puncs` 建立 `punc_mapping`（从 200 开始的稳定 ID 段），确保 '！' 有明确、稳定的编码；
  4. 将 `normalized_puncs` 和 `punc_mapping` 写入保存的 tokenizer 配置，便于推理侧复用。

代码要点（节选）：

- 优先对齐配置标点，然后补齐 CT 提供的其余标点；不改动训练数据与标签体系。

### 验证结果

- 构造包含 '！' 的句子样例进行编码，确认 tokenizer 能对 '！' 生成稳定的 punc_mapping 编码；
- 训练日志未发生兼容性告警，训练/验证流程、日志输出与性能保持不变；
- 推理侧保持与配置一致的标点映射，结果可解释性更好。

### 本次代码更新（2025-08-12）

- 数据目录修正（配置一致性）

  - 修改 export/config.py：DATA_DIR = './export/data'（原为 './data'）
  - 目的：统一训练/推理使用仓库内现有数据目录，提升可重复性

- 推理解码参数调整（Viterbi+密度控制）

  - 类别偏置：COMMA/PAUSE 从 -0.8 调至 -0.6；PERIOD/QUESTION/EXCLAIM 保持 -1.0
  - 转移惩罚：PUNC→PUNC 从 -1.5 调至 -1.8（更强抑制连续标点）
  - 密度控制：滑动窗口从 6 调至 8（后处理阶段）

- 训练性能优化（RTX 4090）
  - 启用 BF16 混合精度：trainer.\_train_epoch 使用 torch.autocast(dtype=torch.bfloat16)
  - DataLoader 加速：num_workers=8, pin_memory=True, persistent_workers=True, prefetch_factor=2

验证与回滚建议：

- 若逗号/顿号仍偏多：增大最小间隔为 4，或将 PUNC→PUNC 提至 -2.0
- 若标点变稀：将 COMMA/PAUSE 偏置从 -0.6 调回 -0.5，或 PUNC→PUNC 降至 -1.6

---

## 微调推理出现“过度标点/顿号过多”问题修复（当前生效方案）

### 问题现象

- 在模型对比演示中，微调模型在数字/时间/里程串、姓名/代号等附近频繁插入“、”“，”等标点；而预训练模型较为正常。

### 综合原因分析

- 训练与推理特征提取不一致：训练端通过在 CT-Transformer 内部挂钩捕获隐层，而推理端此前使用“占位随机特征+嵌入回退”，导致分布漂移，解码时更“贪心”。
- 推理解码抑制不足：顿号的规则过宽，数字串/里程/代号也容易触发。

### 本次修复要点（仅改 inference.py）

1. 训练-推理对齐

- 微调推理模型改为与训练端一致：先尝试在 CT-Transformer 内部模块注册 forward hook 捕获隐层；若失败再整体回退到嵌入特征。取消“随机特征占位”。

2. 顿号/逗号规则收紧

- \_should_use_pause：新增禁用条件（数字/时间/里程/代号匹配，如“二零二五”“K7+500”“W0808”），仅在明显并列短词边界使用。
- \_post_process_punctuation：
  - 清理“单字、单字”样式的顿号；
  - 禁止在连续数字/时间串中出现顿号。

3. 维持 Viterbi 抑制与密度控制

- 保持先前的 Viterbi 类别偏置与 PUNC→PUNC 惩罚（必要时可继续调参）。

### 验证方法

- 使用你给出的真实样例 56-64 做对比，重点观察“二零、二五年…”，“W 零八零八…”，“K 两零零二+五百…”等是否仍出现不合理顿号；
- 与预训练结果对照：微调与预训练风格应更接近（但允许在铁路模板上有更贴近规范的逗号）。

### 调参建议（若仍偏多）

- 将 \_post_process_punctuation 中的最小间隔从 3 提到 4；
- 在 \_viterbi_smooth_labels 中对 PAUSE 类别的 label_bias 再减小 0.1 ～ 0.2；
- 将 PUNC→PUNC 的惩罚从 -1.8 临时提高到 -2.0；

### 数据/标签一致性核查

- 你提供的数据分析显示非 O 标签约 11%，分布健康；数据文件采用“每字一行+无原标点”的格式，与训练管线一致。推理端保持“先去标点再预测”的策略，与标签定义吻合。

---

## 旧问题记录（微调问题深度修复）

以下内容保留原有“微调问题深度修复”的完整记录，便于追踪历史问题与方案。

### 🛠️ 修复方案

1. **重构模型架构**: 创建 CTTransformerForPunctuation，正确集成 CT-Transformer
2. **修复 Tokenizer**: 实现 CTCompatibleTokenizer，保持与 CT-Transformer 兼容
3. **优化标签对齐**: 重写标签映射逻辑，确保精确对齐
4. **改进训练策略**: 启用微调，优化超参数，添加梯度累积

### 🔧 具体修复细节

#### 问题：FunASR AutoModel 参数访问错误

**错误信息**: `'AutoModel' object has no attribute 'parameters'`
**根本原因**: FunASR 的 AutoModel 不是标准 PyTorch 模型，没有 `parameters()` 方法
**修复方案**:

- 移除对 `self.ct_model.parameters()` 的直接调用
- 只训练分类头权重，保持 CT-Transformer 预训练权重冻结
- 在模型保存时只保存可训练部分（分类器权重）

#### 问题：Tokenizer 提取失败

**根本原因**: CT-Transformer 内部 tokenizer 结构复杂，提取方法不够健壮
**修复方案**:

- 改进 `_extract_ct_tokenizer()` 方法，尝试多种属性路径
- 增强 `CTCompatibleTokenizer` 的字符映射逻辑
- 添加异常处理和调试信息

#### 问题：前向传播复杂度过高

**根本原因**: 原始前向传播尝试复杂的文本转换和特征提取
**修复方案**:

- 简化 CT-Transformer 特征提取逻辑
- 添加多层回退机制：CT 特征 → 嵌入特征 → 随机特征
- 优化推理时的特征生成方法

## 项目概述

本项目基于阿里达摩院的 CT-Transformer 模型，实现中文标点符号预测功能，专门针对铁路通信对话场景进行微调。经过深度修复，现已解决微调效果不佳的问题。

## 项目结构

```
ct_transformer/
├── export/
│   ├── main.py                    # 主程序入口
│   ├── config.py                  # 配置文件
│   ├── trainer.py                 # 训练模块
│   ├── inference.py               # 推理模块
│   ├── improved_data_processor.py # 数据处理模块
│   ├── download.py                # 模型下载脚本
│   ├── ct_transformer_model.py    # CT-Transformer模型封装
│   └── test_basic.py              # 基础功能测试
├── model/                         # 下载的模型存储目录
├── data/                          # 训练数据目录
├── output/                        # 输出目录
└── 调试手册.md                    # 本文件
```

## 重构内容

### 1. 配置文件修改 (config.py)

**修改内容：**

- 添加了 `PRETRAINED_MODEL_NAME` 配置，指向官方 CT-Transformer 模型
- 增加了模型路径管理方法：
  - `get_pretrained_model_path()`: 获取预训练模型路径
  - `get_training_model_path()`: 获取训练后模型路径
- 调整了 `MAX_LENGTH` 为 512，支持更长序列

**关键配置：**

```python
PRETRAINED_MODEL_NAME = "iic/punc_ct-transformer_zh-cn-common-vocab272727-pytorch"
DOWNLOADED_MODEL_DIR = './model/downloaded_model'
MODEL_PATH_FILE = './export/model_path.txt'
```

### 2. 主程序重构 (main.py)

**新增功能：**

- 集成了 `analyze_data.py` 的数据分析功能
- 添加了模型下载功能
- 更新了菜单系统，包含 7 个选项：
  1. 下载预训练模型
  2. 训练模型
  3. 测试推理
  4. 生成数据
  5. 分析数据
  6. 查看配置
  7. 退出

**关键功能：**

- `download_pretrained_model()`: 下载官方预训练模型
- `analyze_data()`: 分析训练数据质量
- `analyze_data_file()`: 分析单个数据文件

### 3. 训练模块修改 (trainer.py)

**修改内容：**

- 优先使用下载的 CT-Transformer 模型作为基础模型
- 添加了模型加载的容错机制
- 支持 `ignore_mismatched_sizes=True` 参数处理标签数量不匹配

**关键修改：**

```python
pretrained_model_path = self.config.get_pretrained_model_path()
self.model = AutoModelForTokenClassification.from_pretrained(
    pretrained_model_path,
    num_labels=len(self.config.LABELS),
    id2label=self.config.ID_TO_LABEL,
    label2id=self.config.LABEL_TO_ID,
    ignore_mismatched_sizes=True
)
```

### 4. 推理模块修改 (inference.py)

**修改内容：**

- 优先使用训练后的模型，如果不存在则使用预训练模型
- 添加了多层级的模型加载容错机制
- 支持自动回退到预训练模型

**加载逻辑：**

1. 尝试加载训练后的模型
2. 如果失败，尝试加载预训练模型
3. 如果都失败，抛出异常

## 环境依赖

### 必需依赖

```bash
pip install transformers torch scikit-learn numpy tqdm modelscope
```

### 依赖说明

- `transformers`: Hugging Face Transformers 库，用于模型加载和推理
- `torch`: PyTorch 深度学习框架
- `scikit-learn`: 机器学习库，用于评估指标计算
- `numpy`: 数值计算库
- `tqdm`: 进度条库
- `modelscope`: 阿里 ModelScope 模型库，用于下载预训练模型

## 使用流程

### 1. 环境准备

```bash
# 安装依赖
pip install transformers torch scikit-learn numpy tqdm modelscope

# 运行基础测试
python export/test_basic.py
```

### 2. 下载预训练模型

```bash
# 方法1: 使用主程序
python export/main.py
# 选择选项1: 下载预训练模型

# 方法2: 直接运行下载脚本
python export/download.py
```

### 3. 训练模型

```bash
# 使用主程序
python export/main.py
# 依次选择：
# 4. 生成数据 (生成训练数据)
# 2. 训练模型 (开始训练)
```

### 4. 测试推理

```bash
# 使用主程序
python export/main.py
# 选择选项3: 测试推理
```

## 常见问题及解决方案

### 1. 模块导入错误

**问题：** `ModuleNotFoundError: No module named 'transformers'`

**解决方案：**

```bash
pip install transformers torch scikit-learn numpy tqdm modelscope
```

### 2. 模型下载失败

**问题：** 网络连接问题或 ModelScope 访问失败

**解决方案：**

- 检查网络连接
- 使用代理或 VPN
- 手动下载模型到指定目录

### 3. 模型加载失败

**问题：** 预训练模型与当前标签不匹配

**解决方案：**

- 代码已添加 `ignore_mismatched_sizes=True` 参数
- 如果仍有问题，检查标签配置是否正确

### 4. 内存不足

**问题：** 训练时出现 CUDA 内存不足

**解决方案：**

- 减少 `BATCH_SIZE` (在 config.py 中)
- 减少 `MAX_LENGTH`
- 使用 CPU 训练（设置 `DEVICE = 'cpu'`）

### 5. 数据质量问题

**问题：** 训练效果不佳

**解决方案：**

- 使用菜单选项 5 分析数据质量
- 调整数据生成参数
- 增加训练数据量

## 性能优化建议

### 1. 硬件配置

- **推荐 GPU：** NVIDIA GTX 1060 或更高
- **内存要求：** 至少 8GB RAM
- **存储空间：** 至少 5GB 可用空间

### 2. 训练参数调优

```python
# config.py中的关键参数
BATCH_SIZE = 16        # 根据GPU内存调整
LEARNING_RATE = 2e-5   # 学习率
NUM_EPOCHS = 3         # 训练轮数
MAX_LENGTH = 512       # 序列最大长度
```

### 3. 数据优化

- 生成更多样化的训练数据
- 平衡各类标点符号的分布
- 增加句子长度的多样性

## 模型评估

### 评估指标

- **Accuracy**: 整体准确率
- **Precision**: 精确率
- **Recall**: 召回率
- **F1-Score**: F1 分数

### 评估方法

使用菜单选项 5 分析数据质量，查看：

- 标签分布统计
- 句子长度分析
- 数据质量检查

## 部署建议

### 1. 生产环境部署

- 使用训练后的模型进行推理
- 配置适当的批处理大小
- 实现模型缓存机制

### 2. API 服务

可以基于 `inference.py` 构建 REST API 服务：

```python
from inference import PunctuationPredictor

predictor = PunctuationPredictor()
result = predictor.predict("输入文本")
```

## 后续改进方向

1. **模型优化**

   - 尝试不同的预训练模型
   - 实现模型蒸馏
   - 添加数据增强

2. **功能扩展**

   - 支持批量文件处理
   - 添加 Web 界面
   - 实现实时推理 API

3. **性能提升**
   - 模型量化
   - ONNX 转换
   - GPU 加速优化

## 调试过程记录

### 遇到的问题和解决方案

#### 1. 环境切换问题

**问题：** 在 Windows 环境下，普通的 python 命令无法正确激活 conda 环境

**解决方案：** 使用完整路径运行 Python

```bash
E:/Anaconda/envs/bd/python.exe export/trainer.py
```

#### 2. 模型架构重构

**问题：** 原始代码使用 transformers 库的 AutoModel，与 CT-Transformer 的使用方式不匹配

**解决方案：**

- 重构 trainer.py，使用 FunASR 的 AutoModel 加载 CT-Transformer
- 重构 inference.py，直接使用 CT-Transformer 进行预测
- 移除不必要的 tokenizer 和自定义模型训练逻辑

#### 3. 依赖管理

**问题：** 缺少 funasr 依赖

**解决方案：**

```bash
pip install funasr
```

#### 4. 模型加载配置

**问题：** 需要正确配置 CT-Transformer 模型参数

**解决方案：** 使用官方推荐的配置

```python
self.am = AutoModel(
    model="damo/punc_ct-transformer_zh-cn-common-vocab272727-pytorch",
    model_revision="v2.0.4",
    disable_update=True,
    device=self.device.type
)
```

#### 7. CUDA 索引越界修复

**问题：** "CUDA error: device-side assert triggered" 和 embedding 层索引越界

**根本原因：**

- tokenizer 生成的字符 ID 超出了 embedding 层的词汇表范围
- 字符映射逻辑不安全，可能产生负数或超大索引

**解决方案：**

```python
# 修复前的问题代码
input_ids.append(ord(token) % 21128 + 1000)  # 可能超出范围

# 修复后的安全代码
def _char_to_id(self, char):
    if char in self.special_tokens:
        return self.special_tokens[char]

    char_code = ord(char)
    char_id = (char_code % (self.vocab_size - 103)) + 103
    return min(max(char_id, 103), self.vocab_size - 1)
```

**关键修复点：**

- 明确定义词汇表大小为 21128
- 确保所有 token ID 都在[0, 21127]范围内
- 添加双重边界检查和断言验证
- 在模型 forward 中添加输入验证
- 使用 padding_idx=0 初始化 embedding 层

#### 8. CUDA 设备管理优化

**问题：** CUDA 设备初始化和错误处理不完善

**解决方案：**

- 添加 CUDA 可用性检测和测试
- 实现自动回退机制（CUDA 失败时回退到 CPU）
- 添加详细的 CUDA 错误信息和调试输出
- 安全的设备间模型移动

```python
def _setup_device(self):
    if torch.cuda.is_available():
        try:
            test_tensor = torch.tensor([1.0]).cuda()
            _ = test_tensor + 1
            return torch.device('cuda')
        except Exception as e:
            logger.warning(f"CUDA测试失败，回退到CPU: {e}")
            return torch.device('cpu')
    else:
        return torch.device('cpu')
```

#### 9. 程序逻辑优化（v4.0）

**优化目标：** 提升代码质量、优化资源管理、简化部署流程

**主要优化内容：**

**模型保存优化：**

- 修改训练逻辑，仅在训练完成后保存最优模型
- 避免每个 epoch 都保存，减少磁盘空间占用
- 在内存中跟踪最佳模型状态，训练结束后一次性保存

**目录管理优化：**

- 实现安全的目录创建逻辑 `_ensure_dir_exists()`
- 添加目录存在性检查，避免重复操作
- 统一目录管理接口

**预训练模型缓存管理：**

- 将 FunASR 模型缓存从 `~/.cache` 移至项目本地 `./models/pretrained/`
- 便于项目管理和部署
- 支持离线环境使用

**代码清理：**

- 移除弃用文件：`analyze_data.py`, `ct_transformer_model.py`, `download.py`, `test_basic.py`
- 清理临时缓存和测试文件
- 保留核心功能模块

**关键代码改进：**

```python
# 优化后的训练逻辑
best_model_state = None
for epoch in range(self.config.NUM_EPOCHS):
    # 训练和验证
    if valid_metrics['f1'] > best_valid_f1:
        best_model_state = self.model.state_dict().copy()

# 训练完成后保存最优模型
if best_model_state is not None:
    self.model.load_state_dict(best_model_state)
    self._save_best_model(best_metrics, best_epoch)
```

### 测试结果

#### 训练测试

- ✅ CT-Transformer 模型成功加载
- ✅ 数据生成功能正常
- ✅ 模型信息成功保存

#### 推理测试

- ✅ 模型加载成功
- ✅ 单句预测功能正常
- ✅ 批量预测功能正常
- ✅ 交互式预测功能正常

#### 性能表现

- 模型加载时间：约 10-15 秒
- 单句推理速度：约 100-200 it/s
- 长句推理速度：约 18-50 it/s
- GPU 内存占用：合理范围内

## 最终配置

### 成功的运行命令

```bash
# 训练
E:/Anaconda/envs/bd/python.exe export/trainer.py

# 推理测试
E:/Anaconda/envs/bd/python.exe export/inference.py

# 主程序
E:/Anaconda/envs/bd/python.exe export/main.py
```

### 核心依赖

```bash
pip install funasr torch
```

## 技术支持

如遇到问题，请检查：

1. 依赖是否正确安装（特别是 funasr）
2. 使用正确的 Python 环境路径
3. 网络连接是否正常（模型下载需要）
4. GPU 驱动是否正确安装
5. 参考本手册的调试过程记录

---

**版本信息：** v4.0
**更新日期：** 2025 年 8 月 7 日
**维护者：** AI Assistant
**状态：** 程序逻辑优化完成，生产就绪

- 增加了模型路径管理方法：
  - `get_pretrained_model_path()`: 获取预训练模型路径
  - `get_training_model_path()`: 获取训练后模型路径
- 调整了 `MAX_LENGTH` 为 512，支持更长序列

**关键配置：**

```python
PRETRAINED_MODEL_NAME = "iic/punc_ct-transformer_zh-cn-common-vocab272727-pytorch"
DOWNLOADED_MODEL_DIR = './model/downloaded_model'
MODEL_PATH_FILE = './export/model_path.txt'
```

### 2. 主程序重构 (main.py)

**新增功能：**

- 集成了 `analyze_data.py` 的数据分析功能
- 添加了模型下载功能
- 更新了菜单系统，包含 7 个选项：
  1. 下载预训练模型
  2. 训练模型
  3. 测试推理
  4. 生成数据
  5. 分析数据
  6. 查看配置
  7. 退出

**关键功能：**

- `download_pretrained_model()`: 下载官方预训练模型
- `analyze_data()`: 分析训练数据质量
- `analyze_data_file()`: 分析单个数据文件

### 3. 训练模块修改 (trainer.py)

**修改内容：**

- 优先使用下载的 CT-Transformer 模型作为基础模型
- 添加了模型加载的容错机制
- 支持 `ignore_mismatched_sizes=True` 参数处理标签数量不匹配

**关键修改：**

```python
pretrained_model_path = self.config.get_pretrained_model_path()
self.model = AutoModelForTokenClassification.from_pretrained(
    pretrained_model_path,
    num_labels=len(self.config.LABELS),
    id2label=self.config.ID_TO_LABEL,
    label2id=self.config.LABEL_TO_ID,
    ignore_mismatched_sizes=True
)
```

### 4. 推理模块修改 (inference.py)

**修改内容：**

- 优先使用训练后的模型，如果不存在则使用预训练模型
- 添加了多层级的模型加载容错机制
- 支持自动回退到预训练模型

**加载逻辑：**

1. 尝试加载训练后的模型
2. 如果失败，尝试加载预训练模型
3. 如果都失败，抛出异常

## 环境依赖

### 必需依赖

```bash
pip install transformers torch scikit-learn numpy tqdm modelscope
```

### 依赖说明

- `transformers`: Hugging Face Transformers 库，用于模型加载和推理
- `torch`: PyTorch 深度学习框架
- `scikit-learn`: 机器学习库，用于评估指标计算
- `numpy`: 数值计算库
- `tqdm`: 进度条显示
- `modelscope`: 阿里 ModelScope 模型库，用于下载预训练模型

## 使用流程

### 1. 环境准备

```bash
# 安装依赖
pip install transformers torch scikit-learn numpy tqdm modelscope

# 运行基础测试
python export/test_basic.py
```

### 2. 下载预训练模型

```bash
# 运行主程序
python export/main.py

# 选择选项1：下载预训练模型
```

### 3. 生成训练数据

```bash
# 在主程序中选择选项4：生成数据
# 或直接运行数据处理器
python export/improved_data_processor.py
```

### 4. 训练模型

```bash
# 在主程序中选择选项2：训练模型
# 训练将使用下载的CT-Transformer模型作为基础
```

### 5. 测试推理

```bash
# 在主程序中选择选项3：测试推理
# 推理将优先使用训练后的模型
```

## 常见问题及解决方案

### 1. 模块导入错误

**问题：** `ModuleNotFoundError: No module named 'transformers'`

**解决方案：**

```bash
pip install transformers torch scikit-learn numpy tqdm modelscope
```

### 2. 模型下载失败

**问题：** 网络连接问题或 ModelScope 访问失败

**解决方案：**

- 检查网络连接
- 尝试使用代理
- 手动下载模型到指定目录

### 3. 模型加载失败

**问题：** 预训练模型与当前标签配置不匹配

**解决方案：**

- 使用 `ignore_mismatched_sizes=True` 参数
- 检查标签配置是否正确
- 确认模型路径是否正确

### 4. 训练数据质量问题

**问题：** 标签分布不均衡

**解决方案：**

- 使用数据分析功能检查标签分布
- 调整数据生成策略
- 增加少数类别的样本

### 5. 内存不足

**问题：** 训练时内存溢出

**解决方案：**

- 减少 `BATCH_SIZE`
- 减少 `MAX_LENGTH`
- 使用梯度累积

### 6. 原文文本与标签文本不一致

**问题：** 转化为 txt 文档时，原文文本与标签文本不一致

**解决方案：**

- 去掉所有空格
- 合并连续相同标点
- 以标注后的文本为基准，去除其标点后作为原文本

## 性能优化建议

### 1. 训练优化

- 调整学习率：建议从 `2e-5` 开始
- 使用学习率调度器：`get_linear_schedule_with_warmup`
- 适当的 warmup 比例：`0.1`

### 2. 数据优化

- 确保标签分布均衡
- 增加数据多样性
- 控制句子长度在合理范围内

### 3. 模型优化

- 使用预训练模型进行微调
- 适当的正则化：`weight_decay=0.01`
- 早停机制防止过拟合

## 扩展功能

### 1. 支持更多标点符号

在 `config.py` 中添加新的标签：

```python
LABELS = [
    'O', 'COMMA', 'PERIOD', 'QUESTION', 'EXCLAIM',
    'SEMICOLON', 'COLON', 'QUOTE'  # 新增标点
]
```

### 2. 支持批量文件处理

可以扩展推理模块支持批量处理文本文件。

### 3. Web 服务部署

可以基于 Flask 或 FastAPI 创建 Web 服务接口。

## 技术细节

### 1. 模型架构

- 基础模型：CT-Transformer (Controllable Time-delay Transformer)
- 任务类型：Token Classification
- 标签数量：5 个（O, COMMA, PERIOD, QUESTION, EXCLAIM）

### 2. 数据格式

训练数据格式：

```
字	O
符	O
，	COMMA
这	O
是	O
句	O
子	O
。	PERIOD

```

### 3. 评估指标

- Accuracy: 准确率
- Precision: 精确率
- Recall: 召回率
- F1-Score: F1 分数

## 更新日志

### v2.0 (当前版本)

- 重构为使用官方 CT-Transformer 模型
- 集成数据分析功能
- 添加模型下载功能
- 改进错误处理和容错机制
- 更新配置管理系统

### v1.0 (原始版本)

- 基于 BERT 的标点预测模型
- 基础训练和推理功能
- 简单的数据生成器

## 联系信息

如有问题或建议，请通过以下方式联系：

- 项目仓库：[GitHub 链接]
- 邮箱：[联系邮箱]

---

_最后更新：2025 年 8 月_
