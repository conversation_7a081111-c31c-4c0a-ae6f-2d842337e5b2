# -*- coding: utf-8 -*-
"""
轻量探测：打印 FunASR AutoModel 内部结构，定位可能的编码器模块
用途：为方案B（直接使用 CT 编码器隐层）提供准确接线信息
"""
import os
import sys
import types

import torch

from config import PunctuationConfig


def main():
    try:
        from funasr import AutoModel
    except Exception as e:
        print(f"❌ 无法导入 funasr: {e}")
        return

    cfg = PunctuationConfig()
    cache_dir = cfg.get_pretrained_cache_dir()
    print(f"🔎 加载 CT-Transformer: {cfg.PRETRAINED_MODEL_NAME} ({cfg.MODEL_REVISION})")
    print(f"📦 缓存目录: {cache_dir}")

    m = AutoModel(
        model=cfg.PRETRAINED_MODEL_NAME,
        model_revision=cfg.MODEL_REVISION,
        disable_update=True,
        device=cfg.DEVICE,
        cache_dir=cache_dir,
    )
    print("✅ AutoModel 已加载")

    core = getattr(m, 'model', None)
    if core is None:
        print("⚠️ AutoModel.model 不存在，无法深入探测")
        return

    print(f"🧩 core 类型: {type(core)}")

    # 列出一层属性
    attrs = [a for a in dir(core) if not a.startswith('_')]
    # 标记潜在编码器关键词
    keywords = ['encoder', 'backbone', 'punc', 'punct', 'net', 'module', 'transformer']
    candidates = []
    for a in attrs:
        try:
            val = getattr(core, a)
            t = type(val)
            mark = any(k in a.lower() for k in keywords)
            if mark:
                candidates.append((a, t))
        except Exception:
            pass

    print("\n🔬 可能的编码器相关属性:")
    for name, t in candidates:
        print(f"  - {name}: {t}")

    # 列出 nn.Module 子模块树（前两层）
    print("\n🌲 子模块（前两层）:")
    def list_children(mod, prefix="", depth=0, max_depth=2):
        if depth > max_depth:
            return
        for name, child in mod.named_children():
            print(f"{'  '*depth}{prefix}{name}: {type(child)}")
            list_children(child, prefix=f"{name}.", depth=depth+1, max_depth=max_depth)

    if hasattr(core, 'named_children'):
        list_children(core, max_depth=2)
    else:
        print("(core 不支持 named_children)")

    print("\n💡 提示：记录上面输出中最可能的编码器模块名称，训练器将依据该名称接入隐层。")


if __name__ == "__main__":
    main()

